"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface MathPanelProps {
  deltaT: number
  hitThreshold: number
  activeMissiles: number
  averageSpeed: number
}

export function MathPanel({ deltaT, hitThreshold, activeMissiles, averageSpeed }: MathPanelProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Mathematical Model</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h4 className="font-semibold text-sm">Target Motion</h4>
          <div className="text-xs font-mono bg-muted p-2 rounded">r⃗_T(t+Δt) = r⃗_T(t) + v⃗_T·Δt</div>
          <p className="text-xs text-muted-foreground">Straight line motion with constant velocity</p>
        </div>

        <div className="space-y-2">
          <h4 className="font-semibold text-sm">Interceptor Guidance</h4>
          <div className="text-xs font-mono bg-muted p-2 rounded space-y-1">
            <div>d⃗ = r⃗_T(t) - r⃗_I(t)</div>
            <div>d̂ = d⃗/||d⃗||</div>
            <div>v⃗_I(t) = d̂ · v_I</div>
          </div>
          <p className="text-xs text-muted-foreground">Pure pursuit - always points toward current target</p>
        </div>

        <div className="space-y-2">
          <h4 className="font-semibold text-sm">Interception Condition</h4>
          <div className="text-xs font-mono bg-muted p-2 rounded">
            ||r⃗_T(t) - r⃗_I(t)|| {"<"} {hitThreshold}
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-semibold text-sm">Simulation Parameters</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span>Δt:</span>
              <Badge variant="outline">{deltaT.toFixed(3)}s</Badge>
            </div>
            <div className="flex justify-between">
              <span>Hit Threshold:</span>
              <Badge variant="outline">{hitThreshold}px</Badge>
            </div>
            <div className="flex justify-between">
              <span>Active Missiles:</span>
              <Badge variant="outline">{activeMissiles}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Avg Speed:</span>
              <Badge variant="outline">{averageSpeed.toFixed(1)}</Badge>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-semibold text-sm">Vector Visualization</h4>
          <div className="space-y-1 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-4 h-0.5 bg-red-500"></div>
              <span>Target velocity vectors</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-0.5 bg-blue-500"></div>
              <span>Pursuit direction vectors</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
