"use client"

import { useState, useEffect, useRef, use<PERSON><PERSON>back } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Play, Pause, RotateCcw, Shield, Radar, Target, Zap, Download, FileText } from "lucide-react"
import { MathPanel } from "./math-panel"
import { radarUtils } from "./radar-system"
import { MISSILE_CONFIGS, calculateBallisticTrajectory, type MissileConfig } from "./missile-types"
import { exportSimulationData, exportCSVData } from "./data-export"

interface Missile {
  id: string
  x: number
  y: number
  startX: number
  startY: number
  targetX: number
  targetY: number
  speed: number
  type: "cruise" | "ballistic" | "hypersonic" | "interceptor"
  config: MissileConfig
  active: boolean
  trail: { x: number; y: number }[]
  launchTime: number
  detected: boolean
  tracked: boolean
}

interface Explosion {
  id: string
  x: number
  y: number
  radius: number
  maxRadius: number
  type: "intercept" | "impact"
}

interface RadarSite {
  x: number
  y: number
  range: number
  sweepAngle: number
  sweepSpeed: number
  active: boolean
}

// Mathematical constants
const DELTA_T = 0.016 // 60 FPS timestep
const HIT_THRESHOLD = 15 // Interception distance threshold

// Vector math helper functions
const vectorMagnitude = (v: { x: number; y: number }) => Math.sqrt(v.x * v.x + v.y * v.y)
const vectorNormalize = (v: { x: number; y: number }) => {
  const mag = vectorMagnitude(v)
  return mag > 0 ? { x: v.x / mag, y: v.y / mag } : { x: 0, y: 0 }
}
const vectorSubtract = (a: { x: number; y: number }, b: { x: number; y: number }) => ({
  x: a.x - b.x,
  y: a.y - b.y,
})

export default function Component() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const [isRunning, setIsRunning] = useState(false)
  const [missiles, setMissiles] = useState<Missile[]>([])
  const [explosions, setExplosions] = useState<Explosion[]>([])
  const [currentTime, setCurrentTime] = useState(0)
  const [stats, setStats] = useState({
    threatsLaunched: 0,
    interceptorsLaunched: 0,
    successfulInterceptions: 0,
    missedTargets: 0,
    citiesHit: 0,
  })

  const CANVAS_WIDTH = 800
  const CANVAS_HEIGHT = 600
  const CITY_Y = CANVAS_HEIGHT - 50
  const DEFENSE_SITES = [
    { x: 150, y: CITY_Y - 30 },
    { x: 400, y: CITY_Y - 30 },
    { x: 650, y: CITY_Y - 30 },
  ]

  // Radar sites
  const [radarSites, setRadarSites] = useState<RadarSite[]>([
    { x: 100, y: CITY_Y - 80, range: 200, sweepAngle: 0, sweepSpeed: 0.15, active: true },
    { x: 400, y: CITY_Y - 80, range: 250, sweepAngle: Math.PI / 3, sweepSpeed: 0.12, active: true },
    { x: 700, y: CITY_Y - 80, range: 200, sweepAngle: Math.PI, sweepSpeed: 0.18, active: true },
  ])

  const createThreatMissile = useCallback(
    (missileType: "cruise" | "ballistic" | "hypersonic") => {
      const startX = Math.random() * CANVAS_WIDTH
      const targetX = 100 + Math.random() * (CANVAS_WIDTH - 200) // Target cities area
      const config = MISSILE_CONFIGS[missileType]

      return {
        id: `${missileType}-${Date.now()}-${Math.random()}`,
        x: startX,
        y: 0,
        startX,
        startY: 0,
        targetX,
        targetY: CITY_Y,
        speed: config.speed + Math.random() * 2,
        type: missileType,
        config,
        active: true,
        trail: [],
        launchTime: currentTime,
        detected: false,
        tracked: false,
      }
    },
    [currentTime],
  )

  const createInterceptorMissile = useCallback(
    (targetMissile: Missile) => {
      // Find closest defense site
      const closestSite = DEFENSE_SITES.reduce((closest, site) => {
        const distToCurrent = Math.sqrt(Math.pow(site.x - targetMissile.x, 2) + Math.pow(site.y - targetMissile.y, 2))
        const distToClosest = Math.sqrt(
          Math.pow(closest.x - targetMissile.x, 2) + Math.pow(closest.y - targetMissile.y, 2),
        )
        return distToCurrent < distToClosest ? site : closest
      })

      const config = MISSILE_CONFIGS.interceptor

      return {
        id: `interceptor-${Date.now()}-${Math.random()}`,
        x: closestSite.x,
        y: closestSite.y,
        startX: closestSite.x,
        startY: closestSite.y,
        targetX: targetMissile.x,
        targetY: targetMissile.y,
        speed: config.speed + Math.random(),
        type: "interceptor" as const,
        config,
        active: true,
        trail: [],
        launchTime: currentTime,
        detected: true,
        tracked: true,
      }
    },
    [currentTime],
  )

  // Update radar sweeps
  const updateRadar = useCallback(() => {
    setRadarSites((prev) =>
      prev.map((radar) => ({
        ...radar,
        sweepAngle: (radar.sweepAngle + radar.sweepSpeed) % (2 * Math.PI),
      })),
    )
  }, [])

  // Update missile detection status
  const updateDetection = useCallback(() => {
    setMissiles((prevMissiles) =>
      prevMissiles.map((missile) => {
        if (missile.type === "interceptor") return missile

        let detected = missile.detected
        let tracked = missile.tracked

        radarSites.forEach((radar) => {
          if (!radar.active) return

          const inRange = radarUtils.isInRange(radar.x, radar.y, missile.x, missile.y, radar.range)

          if (inRange) {
            const targetAngle = radarUtils.getTargetAngle(radar.x, radar.y, missile.x, missile.y)
            const inBeam = radarUtils.isInSweepBeam(targetAngle, radar.sweepAngle, Math.PI / 4) // Wider beam

            if (inBeam) {
              // Much higher detection probability
              const detectionProb = Math.min(missile.config.radarCrossSection * 0.95, 0.9)
              if (Math.random() < detectionProb) {
                detected = true
              }
            }

            // Once detected, keep tracking if in range
            if (detected) {
              tracked = true
            }
          }
        })

        return { ...missile, detected, tracked }
      }),
    )
  }, [radarSites])

  const updateMissiles = useCallback(() => {
    setMissiles((prevMissiles) => {
      const updatedMissiles = prevMissiles.map((missile) => {
        if (!missile.active) return missile

        let newX = missile.x
        let newY = missile.y
        const timeElapsed = currentTime - missile.launchTime

        if (missile.type === "ballistic") {
          // Ballistic trajectory calculation
          const trajectory = calculateBallisticTrajectory(
            missile.startX,
            missile.startY,
            missile.targetX,
            missile.targetY,
            timeElapsed * missile.speed * 0.01,
          )
          newX = trajectory.x
          newY = trajectory.y
        } else if (missile.type === "hypersonic") {
          // Hypersonic missiles: fast, slight maneuverability
          const dx = missile.targetX - missile.x
          const dy = missile.targetY - missile.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < 5) {
            return { ...missile, active: false }
          }

          // Add slight random maneuverability
          const maneuverX = (Math.random() - 0.5) * missile.config.maneuverability * 2
          const maneuverY = (Math.random() - 0.5) * missile.config.maneuverability * 2

          const velocity = vectorNormalize({ x: dx + maneuverX, y: dy + maneuverY })
          newX = missile.x + velocity.x * missile.speed * DELTA_T * 60
          newY = missile.y + velocity.y * missile.speed * DELTA_T * 60
        } else if (missile.type === "cruise") {
          // Cruise missiles: moderate speed, high maneuverability
          const dx = missile.targetX - missile.x
          const dy = missile.targetY - missile.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < 5) {
            return { ...missile, active: false }
          }

          const velocity = vectorNormalize({ x: dx, y: dy })
          newX = missile.x + velocity.x * missile.speed * DELTA_T * 60
          newY = missile.y + velocity.y * missile.speed * DELTA_T * 60
        } else if (missile.type === "interceptor") {
          // Pure Pursuit Guidance for interceptors
          const targetMissile = prevMissiles.find(
            (m) =>
              m.type !== "interceptor" &&
              m.active &&
              m.detected &&
              m.tracked &&
              Math.abs(m.targetX - missile.targetX) < 100 &&
              Math.abs(m.targetY - missile.targetY) < 100,
          )

          if (targetMissile) {
            // Step 1: Compute vector from interceptor to target
            const d = vectorSubtract({ x: targetMissile.x, y: targetMissile.y }, { x: missile.x, y: missile.y })

            // Step 2: Normalize to get direction
            const d_hat = vectorNormalize(d)

            // Step 3: Compute interceptor's velocity
            const velocity = {
              x: d_hat.x * missile.speed,
              y: d_hat.y * missile.speed,
            }

            // Step 4: Update position
            newX = missile.x + velocity.x * DELTA_T * 60
            newY = missile.y + velocity.y * DELTA_T * 60

            // Update target position for real-time tracking
            missile.targetX = targetMissile.x
            missile.targetY = targetMissile.y
          } else {
            // Continue to last known target
            const dx = missile.targetX - missile.x
            const dy = missile.targetY - missile.y
            const distance = Math.sqrt(dx * dx + dy * dy)

            if (distance < 5) {
              return { ...missile, active: false }
            }

            const velocity = vectorNormalize({ x: dx, y: dy })
            newX = missile.x + velocity.x * missile.speed * DELTA_T * 60
            newY = missile.y + velocity.y * missile.speed * DELTA_T * 60
          }
        }

        // Update trail
        const newTrail = [...missile.trail, { x: missile.x, y: missile.y }]
        if (newTrail.length > 20) newTrail.shift()

        return {
          ...missile,
          x: newX,
          y: newY,
          trail: newTrail,
        }
      })

      // Check for interceptions
      const activeMissiles = updatedMissiles.filter((m) => m.active)
      const threats = activeMissiles.filter((m) => m.type !== "interceptor")
      const interceptors = activeMissiles.filter((m) => m.type === "interceptor")

      const newExplosions: Explosion[] = []
      const missilesToDeactivate = new Set<string>()

      threats.forEach((threat) => {
        interceptors.forEach((interceptor) => {
          const distance = vectorMagnitude(
            vectorSubtract({ x: threat.x, y: threat.y }, { x: interceptor.x, y: interceptor.y }),
          )

          if (distance < HIT_THRESHOLD) {
            missilesToDeactivate.add(threat.id)
            missilesToDeactivate.add(interceptor.id)
            newExplosions.push({
              id: `explosion-${Date.now()}-${Math.random()}`,
              x: (threat.x + interceptor.x) / 2,
              y: (threat.y + interceptor.y) / 2,
              radius: 0,
              maxRadius: 50,
              type: "intercept",
            })
            setStats((prev) => ({ ...prev, successfulInterceptions: prev.successfulInterceptions + 1 }))
          }
        })

        // Check if threat reached target
        if (threat.y >= CITY_Y - 10 && threat.active) {
          missilesToDeactivate.add(threat.id)
          newExplosions.push({
            id: `explosion-${Date.now()}-${Math.random()}`,
            x: threat.x,
            y: threat.y,
            radius: 0,
            maxRadius: 80,
            type: "impact",
          })
          setStats((prev) => ({
            ...prev,
            missedTargets: prev.missedTargets + 1,
            citiesHit: prev.citiesHit + 1,
          }))
        }
      })

      setExplosions((prev) => [...prev, ...newExplosions])

      return updatedMissiles.map((missile) =>
        missilesToDeactivate.has(missile.id) ? { ...missile, active: false } : missile,
      )
    })
  }, [currentTime])

  const updateExplosions = useCallback(() => {
    setExplosions((prev) =>
      prev
        .map((explosion) => ({
          ...explosion,
          radius: Math.min(explosion.radius + 3, explosion.maxRadius),
        }))
        .filter((explosion) => explosion.radius < explosion.maxRadius),
    )
  }, [])

  const draw = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Clear canvas
    ctx.fillStyle = "#0a0a0a"
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT)

    // Draw stars
    ctx.fillStyle = "#ffffff"
    for (let i = 0; i < 100; i++) {
      const x = (i * 37) % CANVAS_WIDTH
      const y = (i * 23) % (CANVAS_HEIGHT - 100)
      ctx.fillRect(x, y, 1, 1)
    }

    // Draw radar sites and sweeps
    radarSites.forEach((radar) => {
      if (!radar.active) return

      // Draw radar site
      ctx.fillStyle = "#10b981"
      ctx.beginPath()
      ctx.arc(radar.x, radar.y, 6, 0, Math.PI * 2)
      ctx.fill()

      // Draw radar range circle
      ctx.strokeStyle = "#10b981"
      ctx.setLineDash([3, 3])
      ctx.lineWidth = 1
      ctx.beginPath()
      ctx.arc(radar.x, radar.y, radar.range, 0, Math.PI * 2)
      ctx.stroke()

      // Draw radar sweep beam
      const beamWidth = Math.PI / 6
      ctx.fillStyle = "rgba(16, 185, 129, 0.1)"
      ctx.beginPath()
      ctx.moveTo(radar.x, radar.y)
      ctx.arc(radar.x, radar.y, radar.range, radar.sweepAngle - beamWidth / 2, radar.sweepAngle + beamWidth / 2)
      ctx.closePath()
      ctx.fill()

      // Draw sweep line
      ctx.strokeStyle = "#10b981"
      ctx.setLineDash([])
      ctx.lineWidth = 2
      ctx.beginPath()
      ctx.moveTo(radar.x, radar.y)
      ctx.lineTo(radar.x + Math.cos(radar.sweepAngle) * radar.range, radar.y + Math.sin(radar.sweepAngle) * radar.range)
      ctx.stroke()
    })

    // Draw cities
    ctx.fillStyle = "#4a5568"
    for (let i = 0; i < 8; i++) {
      const x = i * 100 + 50
      const height = 20 + ((i * 7) % 30)
      ctx.fillRect(x, CITY_Y - height, 30, height)
    }

    // Draw defense sites
    ctx.fillStyle = "#22c55e"
    DEFENSE_SITES.forEach((site) => {
      ctx.beginPath()
      ctx.arc(site.x, site.y, 8, 0, Math.PI * 2)
      ctx.fill()
    })

    // Draw missiles
    missiles.forEach((missile) => {
      if (!missile.active) return

      // Draw trail
      ctx.strokeStyle = missile.config.trailColor
      ctx.lineWidth = missile.config.size
      ctx.beginPath()
      missile.trail.forEach((point, index) => {
        if (index === 0) {
          ctx.moveTo(point.x, point.y)
        } else {
          ctx.lineTo(point.x, point.y)
        }
      })
      ctx.stroke()

      // Draw missile
      ctx.fillStyle = missile.config.color
      ctx.beginPath()
      ctx.arc(missile.x, missile.y, missile.config.size, 0, Math.PI * 2)
      ctx.fill()

      // Draw detection indicator
      if (missile.detected && missile.type !== "interceptor") {
        ctx.strokeStyle = "#fbbf24"
        ctx.setLineDash([2, 2])
        ctx.lineWidth = 1
        ctx.beginPath()
        ctx.arc(missile.x, missile.y, 12, 0, Math.PI * 2)
        ctx.stroke()
        ctx.setLineDash([])
      }

      // Draw tracking indicator
      if (missile.tracked && missile.type !== "interceptor") {
        ctx.strokeStyle = "#ef4444"
        ctx.setLineDash([])
        ctx.lineWidth = 2
        ctx.beginPath()
        ctx.arc(missile.x, missile.y, 15, 0, Math.PI * 2)
        ctx.stroke()
      }

      // Draw missile type label
      if (missile.type !== "interceptor") {
        ctx.fillStyle = "#ffffff"
        ctx.font = "10px monospace"
        ctx.fillText(missile.type.toUpperCase(), missile.x + 8, missile.y - 8)
      }
    })

    // Draw explosions
    explosions.forEach((explosion) => {
      const alpha = 1 - explosion.radius / explosion.maxRadius
      ctx.fillStyle = explosion.type === "intercept" ? `rgba(34, 197, 94, ${alpha})` : `rgba(239, 68, 68, ${alpha})`
      ctx.beginPath()
      ctx.arc(explosion.x, explosion.y, explosion.radius, 0, Math.PI * 2)
      ctx.fill()

      // Add shockwave effect
      if (explosion.radius > explosion.maxRadius * 0.3) {
        ctx.strokeStyle =
          explosion.type === "intercept" ? `rgba(34, 197, 94, ${alpha * 0.5})` : `rgba(239, 68, 68, ${alpha * 0.5})`
        ctx.lineWidth = 3
        ctx.beginPath()
        ctx.arc(explosion.x, explosion.y, explosion.radius * 1.2, 0, Math.PI * 2)
        ctx.stroke()
      }
    })

    // Draw ground
    ctx.fillStyle = "#1f2937"
    ctx.fillRect(0, CITY_Y, CANVAS_WIDTH, 50)

    // Draw HUD information
    ctx.fillStyle = "#ffffff"
    ctx.font = "12px monospace"
    ctx.fillText(`Time: ${currentTime.toFixed(1)}s`, 10, 20)
    ctx.fillText(`Active Missiles: ${missiles.filter((m) => m.active).length}`, 10, 35)
    ctx.fillText(`Detected: ${missiles.filter((m) => m.detected && m.type !== "interceptor").length}`, 10, 50)
    ctx.fillText(`Tracked: ${missiles.filter((m) => m.tracked && m.type !== "interceptor").length}`, 10, 65)

    // Debug information
    const detectedCount = missiles.filter((m) => m.detected && m.type !== "interceptor").length
    const interceptorCount = missiles.filter((m) => m.type === "interceptor" && m.active).length

    ctx.fillText(`Interceptors Active: ${interceptorCount}`, 10, 80)
    ctx.fillText(
      `Detection Rate: ${detectedCount}/${missiles.filter((m) => m.type !== "interceptor" && m.active).length}`,
      10,
      95,
    )
  }, [missiles, explosions, radarSites, currentTime])

  const animate = useCallback(() => {
    if (!isRunning) return

    setCurrentTime((prev) => prev + DELTA_T)
    updateRadar()
    updateDetection()
    updateMissiles()
    updateExplosions()
    draw()

    // Spawn new threats randomly
    const spawnRate = 0.015
    if (Math.random() < spawnRate) {
      const threatTypes = ["cruise", "ballistic", "hypersonic"] as const
      const randomType = threatTypes[Math.floor(Math.random() * threatTypes.length)]
      const newThreat = createThreatMissile(randomType)
      setMissiles((prev) => [...prev, newThreat])
      setStats((prev) => ({ ...prev, threatsLaunched: prev.threatsLaunched + 1 }))
    }

    // Launch interceptors for detected and tracked threats
    setMissiles((prevMissiles) => {
      const threats = prevMissiles.filter(
        (m) => m.type !== "interceptor" && m.active && m.detected && m.y < CANVAS_HEIGHT * 0.8,
      )
      const interceptors = prevMissiles.filter((m) => m.type === "interceptor" && m.active)
      const newInterceptors: Missile[] = []

      threats.forEach((threat) => {
        const hasInterceptor = interceptors.some((interceptor) => {
          const distance = Math.sqrt(
            Math.pow(threat.x - interceptor.targetX, 2) + Math.pow(threat.y - interceptor.targetY, 2),
          )
          return distance < 120
        })

        const launchProbability = threat.tracked ? 0.8 : 0.4
        if (!hasInterceptor && Math.random() < launchProbability) {
          const newInterceptor = createInterceptorMissile(threat)
          newInterceptors.push(newInterceptor)
          setStats((prev) => ({ ...prev, interceptorsLaunched: prev.interceptorsLaunched + 1 }))
        }
      })

      return newInterceptors.length > 0 ? [...prevMissiles, ...newInterceptors] : prevMissiles
    })

    animationRef.current = requestAnimationFrame(animate)
  }, [
    isRunning,
    updateRadar,
    updateDetection,
    updateMissiles,
    updateExplosions,
    draw,
    createThreatMissile,
    createInterceptorMissile,
  ])

  useEffect(() => {
    if (isRunning) {
      animationRef.current = requestAnimationFrame(animate)
    } else {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isRunning, animate])

  useEffect(() => {
    draw()
  }, [draw])

  const handleStart = () => {
    setIsRunning(true)
  }

  const handlePause = () => {
    setIsRunning(false)
  }

  const handleReset = () => {
    setIsRunning(false)
    setMissiles([])
    setExplosions([])
    setCurrentTime(0)
    setStats({
      threatsLaunched: 0,
      interceptorsLaunched: 0,
      successfulInterceptions: 0,
      missedTargets: 0,
      citiesHit: 0,
    })
  }

  const handleDownloadJSON = () => {
    exportSimulationData(missiles, radarSites, stats, currentTime, DELTA_T, HIT_THRESHOLD, CANVAS_WIDTH, CANVAS_HEIGHT)
  }

  const handleDownloadCSV = () => {
    exportCSVData(missiles, stats, currentTime)
  }

  const successRate =
    stats.interceptorsLaunched > 0 ? Math.round((stats.successfulInterceptions / stats.interceptorsLaunched) * 100) : 0
  const interceptionRate =
    stats.threatsLaunched > 0 ? Math.round((stats.successfulInterceptions / stats.threatsLaunched) * 100) : 0

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-4">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Shield className="w-8 h-8" />
          Advanced Missile Defense System
        </h1>
        <p className="text-muted-foreground">Ballistic, Hypersonic & Cruise Missiles with Radar Detection System</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-4">
              <canvas
                ref={canvasRef}
                width={CANVAS_WIDTH}
                height={CANVAS_HEIGHT}
                className="border rounded-lg w-full max-w-full h-auto bg-black"
                style={{ aspectRatio: `${CANVAS_WIDTH}/${CANVAS_HEIGHT}` }}
              />
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Controls</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button onClick={handleStart} disabled={isRunning} className="w-full">
                <Play className="w-4 h-4 mr-2" />
                Start
              </Button>
              <Button onClick={handlePause} disabled={!isRunning} variant="outline" className="w-full">
                <Pause className="w-4 h-4 mr-2" />
                Pause
              </Button>
              <Button onClick={handleReset} variant="outline" className="w-full">
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Data Export</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                onClick={handleDownloadJSON}
                disabled={isRunning || missiles.length === 0}
                variant="outline"
                className="w-full"
              >
                <Download className="w-4 h-4 mr-2" />
                Download JSON
              </Button>
              <Button
                onClick={handleDownloadCSV}
                disabled={isRunning || missiles.length === 0}
                variant="outline"
                className="w-full"
              >
                <FileText className="w-4 h-4 mr-2" />
                Download CSV
              </Button>
              <p className="text-xs text-muted-foreground">
                Pause simulation to export current data including missile positions, radar status, and combat
                statistics.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Target className="w-5 h-5" />
                Combat Statistics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Threats Launched:</span>
                <Badge variant="outline">{stats.threatsLaunched}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Interceptors Launched:</span>
                <Badge className="bg-blue-500">{stats.interceptorsLaunched}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Successful Interceptions:</span>
                <Badge className="bg-green-500">{stats.successfulInterceptions}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Missed Targets:</span>
                <Badge variant="destructive">{stats.missedTargets}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Cities Hit:</span>
                <Badge variant="destructive">{stats.citiesHit}</Badge>
              </div>
              <hr className="my-2" />
              <div className="flex justify-between items-center">
                <span className="text-sm">Interceptor Success Rate:</span>
                <Badge variant={successRate >= 80 ? "default" : successRate >= 60 ? "secondary" : "destructive"}>
                  {successRate}%
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Overall Interception Rate:</span>
                <Badge
                  variant={interceptionRate >= 80 ? "default" : interceptionRate >= 60 ? "secondary" : "destructive"}
                >
                  {interceptionRate}%
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Radar className="w-5 h-5" />
                Radar System
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-xs space-y-1">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                  <span>Active Radar Sites: {radarSites.filter((r) => r.active).length}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span>Detected: {missiles.filter((m) => m.detected && m.type !== "interceptor").length}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Tracked: {missiles.filter((m) => m.tracked && m.type !== "interceptor").length}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Threat Types
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="space-y-1 text-xs">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Cruise Missiles (Maneuverable)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-red-600 rounded-full"></div>
                  <span>Ballistic Missiles (Parabolic)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-900 rounded-full"></div>
                  <span>Hypersonic (Mach 5+)</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>Interceptor Missiles</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <MathPanel
            deltaT={DELTA_T}
            hitThreshold={HIT_THRESHOLD}
            activeMissiles={missiles.filter((m) => m.active).length}
            averageSpeed={missiles.length > 0 ? missiles.reduce((sum, m) => sum + m.speed, 0) / missiles.length : 0}
          />
        </div>
      </div>
    </div>
  )
}
