"use client"

interface MissileData {
  id: string
  type: string
  launchTime: number
  position: { x: number; y: number }
  target: { x: number; y: number }
  speed: number
  detected: boolean
  tracked: boolean
  active: boolean
}

interface RadarData {
  siteId: number
  position: { x: number; y: number }
  range: number
  sweepAngle: number
  active: boolean
}

interface CombatStats {
  threatsLaunched: number
  interceptorsLaunched: number
  successfulInterceptions: number
  missedTargets: number
  citiesHit: number
  successRate: number
  interceptionRate: number
}

interface SimulationData {
  timestamp: string
  simulationTime: number
  missiles: MissileData[]
  radarSites: RadarData[]
  stats: CombatStats
  systemParameters: {
    deltaT: number
    hitThreshold: number
    canvasWidth: number
    canvasHeight: number
  }
}

export function exportSimulationData(
  missiles: any[],
  radarSites: any[],
  stats: any,
  currentTime: number,
  deltaT: number,
  hitThreshold: number,
  canvasWidth: number,
  canvasHeight: number,
): void {
  const successRate =
    stats.interceptorsLaunched > 0 ? Math.round((stats.successfulInterceptions / stats.interceptorsLaunched) * 100) : 0
  const interceptionRate =
    stats.threatsLaunched > 0 ? Math.round((stats.successfulInterceptions / stats.threatsLaunched) * 100) : 0

  const simulationData: SimulationData = {
    timestamp: new Date().toISOString(),
    simulationTime: currentTime,
    missiles: missiles.map((missile, index) => ({
      id: missile.id,
      type: missile.type,
      launchTime: missile.launchTime,
      position: { x: missile.x, y: missile.y },
      target: { x: missile.targetX, y: missile.targetY },
      speed: missile.speed,
      detected: missile.detected,
      tracked: missile.tracked,
      active: missile.active,
    })),
    radarSites: radarSites.map((radar, index) => ({
      siteId: index + 1,
      position: { x: radar.x, y: radar.y },
      range: radar.range,
      sweepAngle: radar.sweepAngle,
      active: radar.active,
    })),
    stats: {
      ...stats,
      successRate,
      interceptionRate,
    },
    systemParameters: {
      deltaT,
      hitThreshold,
      canvasWidth,
      canvasHeight,
    },
  }

  // Convert to JSON
  const jsonData = JSON.stringify(simulationData, null, 2)

  // Create and download file
  const blob = new Blob([jsonData], { type: "application/json" })
  const url = URL.createObjectURL(blob)
  const link = document.createElement("a")
  link.href = url
  link.download = `missile-defense-simulation-${new Date().toISOString().slice(0, 19).replace(/:/g, "-")}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

export function exportCSVData(missiles: any[], stats: any, currentTime: number): void {
  // Create CSV data for missiles
  const csvHeaders = [
    "Missile_ID",
    "Type",
    "Launch_Time",
    "Current_X",
    "Current_Y",
    "Target_X",
    "Target_Y",
    "Speed",
    "Detected",
    "Tracked",
    "Active",
    "Status",
  ]

  const csvRows = missiles.map((missile) => [
    missile.id,
    missile.type,
    missile.launchTime.toFixed(2),
    missile.x.toFixed(2),
    missile.y.toFixed(2),
    missile.targetX.toFixed(2),
    missile.targetY.toFixed(2),
    missile.speed.toFixed(2),
    missile.detected ? "Yes" : "No",
    missile.tracked ? "Yes" : "No",
    missile.active ? "Active" : "Inactive",
    missile.active ? "In-Flight" : "Terminated",
  ])

  // Add summary statistics
  const summaryRows = [
    [""],
    ["SIMULATION SUMMARY"],
    ["Simulation Time (s)", currentTime.toFixed(2)],
    ["Threats Launched", stats.threatsLaunched],
    ["Interceptors Launched", stats.interceptorsLaunched],
    ["Successful Interceptions", stats.successfulInterceptions],
    ["Missed Targets", stats.missedTargets],
    ["Cities Hit", stats.citiesHit],
    [
      "Interceptor Success Rate (%)",
      stats.interceptorsLaunched > 0
        ? Math.round((stats.successfulInterceptions / stats.interceptorsLaunched) * 100)
        : 0,
    ],
    [
      "Overall Interception Rate (%)",
      stats.threatsLaunched > 0 ? Math.round((stats.successfulInterceptions / stats.threatsLaunched) * 100) : 0,
    ],
  ]

  const allRows = [csvHeaders, ...csvRows, ...summaryRows]
  const csvContent = allRows.map((row) => row.join(",")).join("\n")

  // Create and download CSV file
  const blob = new Blob([csvContent], { type: "text/csv" })
  const url = URL.createObjectURL(blob)
  const link = document.createElement("a")
  link.href = url
  link.download = `missile-defense-data-${new Date().toISOString().slice(0, 19).replace(/:/g, "-")}.csv`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}
