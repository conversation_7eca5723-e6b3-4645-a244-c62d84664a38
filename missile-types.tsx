"use client"

export interface MissileConfig {
  type: "cruise" | "ballistic" | "hypersonic" | "interceptor"
  speed: number
  maneuverability: number
  radarCrossSection: number
  color: string
  trailColor: string
  size: number
}

export const MISSILE_CONFIGS: Record<string, MissileConfig> = {
  cruise: {
    type: "cruise",
    speed: 2,
    maneuverability: 0.8,
    radarCrossSection: 1.0,
    color: "#ef4444",
    trailColor: "#ef4444",
    size: 3,
  },
  ballistic: {
    type: "ballistic",
    speed: 4,
    maneuverability: 0.1,
    radarCrossSection: 1.5,
    color: "#dc2626",
    trailColor: "#dc2626",
    size: 5,
  },
  hypersonic: {
    type: "hypersonic",
    speed: 8,
    maneuverability: 0.3,
    radarCrossSection: 0.5,
    color: "#991b1b",
    trailColor: "#fbbf24",
    size: 4,
  },
  interceptor: {
    type: "interceptor",
    speed: 6,
    maneuverability: 1.0,
    radarCrossSection: 0.3,
    color: "#3b82f6",
    trailColor: "#3b82f6",
    size: 3,
  },
}

export function calculateBallisticTrajectory(
  startX: number,
  startY: number,
  targetX: number,
  targetY: number,
  t: number,
): { x: number; y: number } {
  // Simplified ballistic trajectory calculation
  const totalTime = 1.0 // Normalized time
  const progress = Math.min(t / totalTime, 1)

  // Parabolic arc calculation
  const midX = (startX + targetX) / 2
  const midY = Math.min(startY, targetY) - 150 // Arc height

  // Quadratic Bezier curve for ballistic trajectory
  const x = Math.pow(1 - progress, 2) * startX + 2 * (1 - progress) * progress * midX + Math.pow(progress, 2) * targetX

  const y = Math.pow(1 - progress, 2) * startY + 2 * (1 - progress) * progress * midY + Math.pow(progress, 2) * targetY

  return { x, y }
}
