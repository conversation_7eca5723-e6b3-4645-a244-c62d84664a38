"use client"

interface RadarProps {
  x: number
  y: number
  range: number
  sweepAngle: number
  detectedTargets: Array<{ x: number; y: number; type: string }>
}

export function RadarSystem({ x, y, range, sweepAngle, detectedTargets }: RadarProps) {
  return null // This is just for the radar logic, visual rendering happens in main canvas
}

// Radar utility functions
export const radarUtils = {
  isInRange: (radarX: number, radarY: number, targetX: number, targetY: number, range: number): boolean => {
    const distance = Math.sqrt(Math.pow(targetX - radarX, 2) + Math.pow(targetY - radarY, 2))
    return distance <= range
  },

  getTargetAngle: (radarX: number, radarY: number, targetX: number, targetY: number): number => {
    return Math.atan2(targetY - radarY, targetX - radarX)
  },

  isInSweepBeam: (targetAngle: number, sweepAngle: number, beamWidth: number = Math.PI / 6): boolean => {
    const angleDiff = Math.abs(targetAngle - sweepAngle)
    return angleDiff <= beamWidth / 2 || angleDiff >= 2 * Math.PI - beamWidth / 2
  },
}
